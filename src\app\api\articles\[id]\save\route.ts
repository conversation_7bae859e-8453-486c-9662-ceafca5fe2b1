import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { userSavedArticles, articles } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// POST /api/articles/[id]/save - 保存文章
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 检查文章是否存在
    const article = await db
      .select()
      .from(articles)
      .where(eq(articles.id, params.id))
      .limit(1);

    if (!article[0]) {
      return NextResponse.json(
        { error: '文章不存在' },
        { status: 404 }
      );
    }

    // 检查是否已经保存
    const existingSave = await db
      .select()
      .from(userSavedArticles)
      .where(
        and(
          eq(userSavedArticles.userId, session.user.id),
          eq(userSavedArticles.articleId, params.id)
        )
      )
      .limit(1);

    if (existingSave[0]) {
      return NextResponse.json(
        { error: '文章已保存' },
        { status: 400 }
      );
    }

    // 保存文章
    await db
      .insert(userSavedArticles)
      .values({
        userId: session.user.id,
        articleId: params.id,
      });

    return NextResponse.json({
      message: '文章保存成功',
    });
  } catch (error) {
    console.error('保存文章失败:', error);
    return NextResponse.json(
      { error: '保存文章失败' },
      { status: 500 }
    );
  }
}

// DELETE /api/articles/[id]/save - 取消保存文章
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 删除保存记录
    const result = await db
      .delete(userSavedArticles)
      .where(
        and(
          eq(userSavedArticles.userId, session.user.id),
          eq(userSavedArticles.articleId, params.id)
        )
      );

    return NextResponse.json({
      message: '取消保存成功',
    });
  } catch (error) {
    console.error('取消保存文章失败:', error);
    return NextResponse.json(
      { error: '取消保存文章失败' },
      { status: 500 }
    );
  }
}
