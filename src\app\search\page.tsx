'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ArticleCard } from '@/components/dashboard/article-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  SearchIcon, 
  FilterIcon, 
  SortAscIcon,
  ClockIcon,
  TrendingUpIcon,
  FileTextIcon
} from 'lucide-react';

interface SearchResult {
  id: string;
  title: string;
  summary?: string;
  author?: string;
  publishedAt?: Date;
  tags: string[];
  category?: string;
  url?: string;
  createdAt: Date;
  relevanceScore: number;
  source: {
    id: string;
    name: string;
    category: string;
  };
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export default function SearchPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [sortBy, setSortBy] = useState<string>('relevance');
  const [showSubscribedOnly, setShowSubscribedOnly] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  // 重定向未认证用户
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  // 执行搜索
  const performSearch = async (query: string, page = 1, reset = false) => {
    if (!session || !query.trim()) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        q: query,
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        subscribed: showSubscribedOnly.toString(),
      });

      if (selectedCategory) {
        params.set('category', selectedCategory);
      }

      const response = await fetch(`/api/search?${params}`);
      const data = await response.json();

      if (response.ok) {
        if (reset || page === 1) {
          setSearchResults(data.articles);
        } else {
          setSearchResults(prev => [...prev, ...data.articles]);
        }
        setPagination(data.pagination);
        setSuggestions(data.suggestions || []);
      } else {
        console.error('搜索失败:', data.error);
        setSearchResults([]);
        setPagination({ page: 1, limit: 20, total: 0, totalPages: 0 });
      }
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索提交
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      performSearch(searchQuery, 1, true);
      // 更新URL
      const newParams = new URLSearchParams(searchParams);
      newParams.set('q', searchQuery);
      router.push(`/search?${newParams.toString()}`);
    }
  };

  // 初始搜索
  useEffect(() => {
    const query = searchParams.get('q');
    if (query && session) {
      setSearchQuery(query);
      performSearch(query, 1, true);
    }
  }, [session, searchParams]);

  // 当过滤条件改变时重新搜索
  useEffect(() => {
    if (searchQuery && session) {
      performSearch(searchQuery, 1, true);
    }
  }, [selectedCategory, sortBy, showSubscribedOnly, session]);

  // 保存文章
  const handleSaveArticle = async (articleId: string) => {
    try {
      const response = await fetch(`/api/articles/${articleId}/save`, {
        method: 'POST',
      });

      if (response.ok) {
        setSearchResults(prev => 
          prev.map(article => 
            article.id === articleId 
              ? { ...article, isSaved: true }
              : article
          )
        );
      }
    } catch (error) {
      console.error('保存文章失败:', error);
    }
  };

  // 分享文章
  const handleShareArticle = (article: SearchResult) => {
    if (navigator.share) {
      navigator.share({
        title: article.title,
        text: article.summary,
        url: article.url,
      });
    } else {
      const shareText = `${article.title}\n${article.summary}\n${article.url}`;
      navigator.clipboard.writeText(shareText);
      alert('文章链接已复制到剪贴板');
    }
  };

  // 加载更多
  const loadMore = () => {
    if (pagination.page < pagination.totalPages) {
      performSearch(searchQuery, pagination.page + 1, false);
    }
  };

  const categories = [
    { value: '', label: '全部' },
    { value: 'regulatory', label: '监管' },
    { value: 'industry', label: '行业' },
    { value: 'market', label: '市场' },
    { value: 'clinical', label: '临床' },
    { value: 'patents', label: '专利' },
    { value: 'news', label: '新闻' },
  ];

  const sortOptions = [
    { value: 'relevance', label: '相关性' },
    { value: 'date', label: '时间' },
    { value: 'title', label: '标题' },
  ];

  if (status === 'loading') {
    return <div className="flex items-center justify-center min-h-screen">加载中...</div>;
  }

  if (!session) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">搜索</h1>
        <p className="text-muted-foreground">
          在IVD行业资讯中搜索您感兴趣的内容
        </p>
      </div>

      {/* 搜索框 */}
      <form onSubmit={handleSearch} className="mb-6">
        <div className="flex gap-4">
          <div className="flex-1 relative">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="输入搜索关键词..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button type="submit" disabled={loading || !searchQuery.trim()}>
            {loading ? '搜索中...' : '搜索'}
          </Button>
        </div>
      </form>

      {/* 搜索建议 */}
      {suggestions.length > 0 && (
        <div className="mb-6">
          <p className="text-sm text-muted-foreground mb-2">相关建议：</p>
          <div className="flex gap-2 flex-wrap">
            {suggestions.map((suggestion, index) => (
              <Badge
                key={index}
                variant="outline"
                className="cursor-pointer"
                onClick={() => {
                  setSearchQuery(suggestion);
                  performSearch(suggestion, 1, true);
                }}
              >
                {suggestion}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* 过滤和排序 */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex gap-2 flex-wrap">
          <Button
            variant={showSubscribedOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setShowSubscribedOnly(!showSubscribedOnly)}
          >
            <FilterIcon className="h-4 w-4 mr-2" />
            {showSubscribedOnly ? '已订阅' : '全部'}
          </Button>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                排序: {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* 分类过滤 */}
        <div className="flex gap-2 overflow-x-auto">
          {categories.map((category) => (
            <Badge
              key={category.value}
              variant={selectedCategory === category.value ? "default" : "outline"}
              className="cursor-pointer whitespace-nowrap"
              onClick={() => setSelectedCategory(category.value)}
            >
              {category.label}
            </Badge>
          ))}
        </div>
      </div>

      {/* 搜索结果统计 */}
      {searchQuery && (
        <div className="mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <FileTextIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      找到 <strong>{pagination.total}</strong> 篇相关文章
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <ClockIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      搜索关键词: "{searchQuery}"
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 搜索结果 */}
      <div className="space-y-6">
        {searchResults.map((article) => (
          <div key={article.id} className="relative">
            <ArticleCard
              article={article}
              onSave={handleSaveArticle}
              onShare={handleShareArticle}
              onRead={(id) => console.log('阅读文章:', id)}
            />
            {/* 相关性分数显示（仅在相关性排序时显示） */}
            {sortBy === 'relevance' && (
              <div className="absolute top-2 right-2">
                <Badge variant="outline" className="text-xs">
                  相关性: {article.relevanceScore}
                </Badge>
              </div>
            )}
          </div>
        ))}

        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-muted-foreground">搜索中...</p>
          </div>
        )}

        {!loading && searchQuery && searchResults.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">未找到相关文章</p>
            <p className="text-sm text-muted-foreground mt-2">
              尝试使用不同的关键词或调整搜索条件
            </p>
          </div>
        )}

        {!loading && !searchQuery && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">请输入搜索关键词</p>
          </div>
        )}

        {!loading && pagination.page < pagination.totalPages && (
          <div className="text-center py-6">
            <Button onClick={loadMore} variant="outline">
              加载更多结果
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
