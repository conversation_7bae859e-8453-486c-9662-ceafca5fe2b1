'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { ArticleCard } from '@/components/dashboard/article-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  SearchIcon, 
  FilterIcon, 
  RefreshCwIcon,
  BookmarkIcon,
  TrendingUpIcon,
  ClockIcon
} from 'lucide-react';

interface Article {
  id: string;
  title: string;
  summary?: string;
  author?: string;
  publishedAt?: Date;
  tags: string[];
  category?: string;
  url?: string;
  createdAt: Date;
  source: {
    id: string;
    name: string;
    category: string;
  };
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [showSubscribedOnly, setShowSubscribedOnly] = useState(true);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  });

  // 重定向未认证用户
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  // 获取文章列表
  const fetchArticles = async (page = 1, reset = false) => {
    if (!session) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        subscribed: showSubscribedOnly.toString(),
      });

      if (searchQuery) {
        params.set('search', searchQuery);
      }

      if (selectedCategory) {
        params.set('category', selectedCategory);
      }

      const response = await fetch(`/api/articles?${params}`);
      const data = await response.json();

      if (response.ok) {
        if (reset || page === 1) {
          setArticles(data.articles);
        } else {
          setArticles(prev => [...prev, ...data.articles]);
        }
        setPagination(data.pagination);
      } else {
        console.error('获取文章失败:', data.error);
      }
    } catch (error) {
      console.error('获取文章失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    if (session) {
      fetchArticles(1, true);
    }
  }, [session, searchQuery, selectedCategory, showSubscribedOnly]);

  // 保存文章
  const handleSaveArticle = async (articleId: string) => {
    try {
      const response = await fetch(`/api/articles/${articleId}/save`, {
        method: 'POST',
      });

      if (response.ok) {
        // 更新文章状态
        setArticles(prev => 
          prev.map(article => 
            article.id === articleId 
              ? { ...article, isSaved: true }
              : article
          )
        );
      } else {
        const data = await response.json();
        console.error('保存文章失败:', data.error);
      }
    } catch (error) {
      console.error('保存文章失败:', error);
    }
  };

  // 分享文章
  const handleShareArticle = (article: Article) => {
    if (navigator.share) {
      navigator.share({
        title: article.title,
        text: article.summary,
        url: article.url,
      });
    } else {
      // 复制到剪贴板
      const shareText = `${article.title}\n${article.summary}\n${article.url}`;
      navigator.clipboard.writeText(shareText);
      alert('文章链接已复制到剪贴板');
    }
  };

  // 阅读文章
  const handleReadArticle = (articleId: string) => {
    // 标记为已读的逻辑可以在这里实现
    console.log('阅读文章:', articleId);
  };

  // 加载更多
  const loadMore = () => {
    if (pagination.page < pagination.totalPages) {
      fetchArticles(pagination.page + 1, false);
    }
  };

  const categories = [
    { value: '', label: '全部' },
    { value: 'regulatory', label: '监管' },
    { value: 'industry', label: '行业' },
    { value: 'market', label: '市场' },
    { value: 'clinical', label: '临床' },
    { value: 'patents', label: '专利' },
    { value: 'news', label: '新闻' },
  ];

  if (status === 'loading') {
    return <div className="flex items-center justify-center min-h-screen">加载中...</div>;
  }

  if (!session) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">IVD行业资讯</h1>
        <p className="text-muted-foreground">
          欢迎回来，{session.user?.email}！获取最新的体外诊断行业动态。
        </p>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">今日新文章</CardTitle>
            <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pagination.total}</div>
            <p className="text-xs text-muted-foreground">
              较昨日增长 12%
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已保存文章</CardTitle>
            <BookmarkIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24</div>
            <p className="text-xs text-muted-foreground">
              本周新增 8 篇
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">阅读时间</CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.5小时</div>
            <p className="text-xs text-muted-foreground">
              本周总计
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和过滤 */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索文章..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          <Button
            variant={showSubscribedOnly ? "default" : "outline"}
            onClick={() => setShowSubscribedOnly(!showSubscribedOnly)}
          >
            <FilterIcon className="h-4 w-4 mr-2" />
            {showSubscribedOnly ? '已订阅' : '全部'}
          </Button>

          <Button
            variant="outline"
            onClick={() => fetchArticles(1, true)}
            disabled={loading}
          >
            <RefreshCwIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* 分类过滤 */}
      <div className="flex gap-2 mb-6 overflow-x-auto">
        {categories.map((category) => (
          <Badge
            key={category.value}
            variant={selectedCategory === category.value ? "default" : "outline"}
            className="cursor-pointer whitespace-nowrap"
            onClick={() => setSelectedCategory(category.value)}
          >
            {category.label}
          </Badge>
        ))}
      </div>

      {/* 文章列表 */}
      <div className="space-y-6">
        {articles.map((article) => (
          <ArticleCard
            key={article.id}
            article={article}
            onSave={handleSaveArticle}
            onShare={handleShareArticle}
            onRead={handleReadArticle}
          />
        ))}

        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-muted-foreground">加载中...</p>
          </div>
        )}

        {!loading && articles.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">暂无文章</p>
          </div>
        )}

        {!loading && pagination.page < pagination.totalPages && (
          <div className="text-center py-6">
            <Button onClick={loadMore} variant="outline">
              加载更多
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
