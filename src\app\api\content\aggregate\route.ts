import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { ContentScheduler } from '@/services/scheduler';
import { z } from 'zod';

const triggerAggregationSchema = z.object({
  sourceId: z.string().uuid().optional(),
  immediate: z.boolean().default(false),
});

// POST /api/content/aggregate - 触发内容聚合
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // TODO: 检查管理员权限
    // if (!isAdmin(session.user.id)) {
    //   return NextResponse.json(
    //     { error: '权限不足' },
    //     { status: 403 }
    //   );
    // }

    const body = await request.json();
    const { sourceId, immediate } = triggerAggregationSchema.parse(body);

    let job;
    
    if (sourceId) {
      // 触发单个新闻源聚合
      job = await ContentScheduler.triggerSourceAggregation(sourceId);
    } else {
      // 触发全量聚合
      job = await ContentScheduler.triggerFullAggregation();
    }

    return NextResponse.json({
      message: sourceId ? '单个新闻源聚合任务已启动' : '全量聚合任务已启动',
      jobId: job.id,
    });
  } catch (error) {
    console.error('触发内容聚合失败:', error);
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: '输入数据无效', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '触发内容聚合失败' },
      { status: 500 }
    );
  }
}

// GET /api/content/aggregate/status - 获取聚合状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const status = await ContentScheduler.getQueueStatus();

    return NextResponse.json({
      status,
      timestamp: new Date(),
    });
  } catch (error) {
    console.error('获取聚合状态失败:', error);
    return NextResponse.json(
      { error: '获取聚合状态失败' },
      { status: 500 }
    );
  }
}
