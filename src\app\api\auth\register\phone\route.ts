import { NextRequest, NextResponse } from 'next/server';
import { phoneRegisterSchema, createUser, verifySMSCode } from '@/lib/auth';
import { db } from '@/db';
import { users } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = phoneRegisterSchema.parse(body);

    // Verify SMS code
    const isCodeValid = await verifySMSCode(validatedData.phone, validatedData.code);
    if (!isCodeValid) {
      return NextResponse.json(
        { error: '验证码无效或已过期' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.phone, validatedData.phone))
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json(
        { error: '该手机号已注册' },
        { status: 400 }
      );
    }

    // Create new user
    const newUser = await createUser({
      phone: validatedData.phone,
      password: validatedData.password,
      authProvider: 'phone',
    });

    // Return user without password
    const { passwordHash, ...userWithoutPassword } = newUser;

    return NextResponse.json({
      message: '注册成功',
      user: userWithoutPassword,
    });
  } catch (error) {
    console.error('Phone registration error:', error);
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: '输入数据无效', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    );
  }
}
