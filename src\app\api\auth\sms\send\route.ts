import { NextRequest, NextResponse } from 'next/server';
import { sendSMSVerification } from '@/lib/auth';
import { z } from 'zod';

const sendSMSSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号'),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { phone } = sendSMSSchema.parse(body);

    // Rate limiting check (simple implementation)
    // TODO: Implement proper rate limiting with Redis
    
    const success = await sendSMSVerification(phone);
    
    if (success) {
      return NextResponse.json({
        message: '验证码已发送',
      });
    } else {
      return NextResponse.json(
        { error: '发送验证码失败，请稍后重试' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('SMS send error:', error);
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: '手机号格式无效' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '发送验证码失败，请稍后重试' },
      { status: 500 }
    );
  }
}
