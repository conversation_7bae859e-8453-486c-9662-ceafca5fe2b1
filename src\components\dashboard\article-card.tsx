'use client';

import { useState } from 'react';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { <PERSON>, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BookmarkIcon, 
  ShareIcon, 
  ExternalLinkIcon,
  ClockIcon,
  UserIcon,
  TagIcon
} from 'lucide-react';

interface ArticleCardProps {
  article: {
    id: string;
    title: string;
    summary?: string;
    author?: string;
    publishedAt?: Date;
    tags: string[];
    category?: string;
    url?: string;
    createdAt: Date;
    source: {
      id: string;
      name: string;
      category: string;
    };
  };
  onSave?: (articleId: string) => void;
  onShare?: (article: any) => void;
  onRead?: (articleId: string) => void;
  isSaved?: boolean;
  isRead?: boolean;
}

export function ArticleCard({ 
  article, 
  onSave, 
  onShare, 
  onRead,
  isSaved = false,
  isRead = false 
}: ArticleCardProps) {
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (!onSave || saving) return;
    
    setSaving(true);
    try {
      await onSave(article.id);
    } finally {
      setSaving(false);
    }
  };

  const handleRead = () => {
    if (onRead) {
      onRead(article.id);
    }
    
    // 打开文章链接
    if (article.url) {
      window.open(article.url, '_blank');
    }
  };

  const handleShare = () => {
    if (onShare) {
      onShare(article);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      regulatory: 'bg-red-100 text-red-800',
      industry: 'bg-blue-100 text-blue-800',
      market: 'bg-green-100 text-green-800',
      clinical: 'bg-purple-100 text-purple-800',
      patents: 'bg-orange-100 text-orange-800',
      news: 'bg-gray-100 text-gray-800',
    };
    return colors[category] || colors.news;
  };

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      regulatory: '监管',
      industry: '行业',
      market: '市场',
      clinical: '临床',
      patents: '专利',
      news: '新闻',
    };
    return labels[category] || category;
  };

  return (
    <Card className={`transition-all hover:shadow-md ${isRead ? 'opacity-75' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1">
            <h3 
              className="font-semibold text-lg leading-tight cursor-pointer hover:text-primary transition-colors"
              onClick={handleRead}
            >
              {article.title}
            </h3>
            <div className="flex items-center gap-2 mt-2 text-sm text-muted-foreground">
              <span className="font-medium">{article.source.name}</span>
              {article.author && (
                <>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <UserIcon className="h-3 w-3" />
                    <span>{article.author}</span>
                  </div>
                </>
              )}
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            {article.category && (
              <Badge className={getCategoryColor(article.category)}>
                {getCategoryLabel(article.category)}
              </Badge>
            )}
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <ClockIcon className="h-3 w-3" />
              <span>
                {article.publishedAt 
                  ? formatDistanceToNow(new Date(article.publishedAt), { 
                      addSuffix: true, 
                      locale: zhCN 
                    })
                  : formatDistanceToNow(new Date(article.createdAt), { 
                      addSuffix: true, 
                      locale: zhCN 
                    })
                }
              </span>
            </div>
          </div>
        </div>
      </CardHeader>

      {article.summary && (
        <CardContent className="pt-0">
          <p className="text-sm text-muted-foreground line-clamp-3">
            {article.summary}
          </p>
        </CardContent>
      )}

      <CardFooter className="pt-0">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2">
            {article.tags.length > 0 && (
              <div className="flex items-center gap-1">
                <TagIcon className="h-3 w-3 text-muted-foreground" />
                <div className="flex gap-1">
                  {article.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {article.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{article.tags.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSave}
              disabled={saving}
              className={isSaved ? 'text-primary' : ''}
            >
              <BookmarkIcon className={`h-4 w-4 ${isSaved ? 'fill-current' : ''}`} />
              {saving ? '保存中...' : isSaved ? '已保存' : '保存'}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
            >
              <ShareIcon className="h-4 w-4" />
              分享
            </Button>

            {article.url && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRead}
              >
                <ExternalLinkIcon className="h-4 w-4" />
                阅读
              </Button>
            )}
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
