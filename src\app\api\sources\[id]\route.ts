import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { newsSources } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { z } from 'zod';

const updateSourceSchema = z.object({
  name: z.string().min(1, '新闻源名称不能为空').optional(),
  category: z.enum(['regulatory', 'industry', 'market', 'clinical', 'patents', 'news']).optional(),
  baseUrl: z.string().url('请输入有效的URL').optional(),
  apiConfig: z.object({
    type: z.enum(['rss', 'api', 'web_scraping']),
    feedUrl: z.string().url().optional(),
    endpoint: z.string().optional(),
    apiKey: z.string().optional(),
    selectors: z.record(z.string()).optional(),
  }).optional(),
  updateFrequency: z.number().min(1, '更新频率必须大于0').optional(),
  isActive: z.boolean().optional(),
});

// GET /api/sources/[id] - 获取单个新闻源详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const source = await db
      .select()
      .from(newsSources)
      .where(eq(newsSources.id, params.id))
      .limit(1);

    if (!source[0]) {
      return NextResponse.json(
        { error: '新闻源不存在' },
        { status: 404 }
      );
    }

    return NextResponse.json({ source: source[0] });
  } catch (error) {
    console.error('获取新闻源详情失败:', error);
    return NextResponse.json(
      { error: '获取新闻源详情失败' },
      { status: 500 }
    );
  }
}

// PUT /api/sources/[id] - 更新新闻源（管理员功能）
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // TODO: 检查管理员权限
    // if (!isAdmin(session.user.id)) {
    //   return NextResponse.json(
    //     { error: '权限不足' },
    //     { status: 403 }
    //   );
    // }

    const body = await request.json();
    const validatedData = updateSourceSchema.parse(body);

    // 检查新闻源是否存在
    const existingSource = await db
      .select()
      .from(newsSources)
      .where(eq(newsSources.id, params.id))
      .limit(1);

    if (!existingSource[0]) {
      return NextResponse.json(
        { error: '新闻源不存在' },
        { status: 404 }
      );
    }

    const [updatedSource] = await db
      .update(newsSources)
      .set(validatedData)
      .where(eq(newsSources.id, params.id))
      .returning();

    return NextResponse.json({
      message: '新闻源更新成功',
      source: updatedSource,
    });
  } catch (error) {
    console.error('更新新闻源失败:', error);
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: '输入数据无效', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '更新新闻源失败' },
      { status: 500 }
    );
  }
}

// DELETE /api/sources/[id] - 删除新闻源（管理员功能）
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // TODO: 检查管理员权限
    // if (!isAdmin(session.user.id)) {
    //   return NextResponse.json(
    //     { error: '权限不足' },
    //     { status: 403 }
    //   );
    // }

    // 检查新闻源是否存在
    const existingSource = await db
      .select()
      .from(newsSources)
      .where(eq(newsSources.id, params.id))
      .limit(1);

    if (!existingSource[0]) {
      return NextResponse.json(
        { error: '新闻源不存在' },
        { status: 404 }
      );
    }

    // 软删除：设置为不活跃状态
    await db
      .update(newsSources)
      .set({ isActive: false })
      .where(eq(newsSources.id, params.id));

    return NextResponse.json({
      message: '新闻源删除成功',
    });
  } catch (error) {
    console.error('删除新闻源失败:', error);
    return NextResponse.json(
      { error: '删除新闻源失败' },
      { status: 500 }
    );
  }
}
