import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { articles, newsSources, userSubscriptions } from '@/db/schema';
import { eq, and, or, ilike, desc, inArray, sql } from 'drizzle-orm';

// GET /api/search - 全文搜索文章
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const category = searchParams.get('category');
    const sourceId = searchParams.get('sourceId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const subscribed = searchParams.get('subscribed') === 'true';
    const sortBy = searchParams.get('sortBy') || 'relevance'; // relevance, date, title
    const offset = (page - 1) * limit;

    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: '搜索关键词不能为空' },
        { status: 400 }
      );
    }

    // 构建基础查询
    let searchQuery = db
      .select({
        id: articles.id,
        sourceId: articles.sourceId,
        title: articles.title,
        content: articles.content,
        summary: articles.summary,
        author: articles.author,
        publishedAt: articles.publishedAt,
        tags: articles.tags,
        category: articles.category,
        url: articles.url,
        createdAt: articles.createdAt,
        source: {
          id: newsSources.id,
          name: newsSources.name,
          category: newsSources.category,
        },
        // 计算相关性分数
        relevanceScore: sql<number>`
          CASE 
            WHEN ${articles.title} ILIKE ${`%${query}%`} THEN 3
            WHEN ${articles.summary} ILIKE ${`%${query}%`} THEN 2
            WHEN ${articles.content} ILIKE ${`%${query}%`} THEN 1
            ELSE 0
          END
        `,
      })
      .from(articles)
      .innerJoin(newsSources, eq(articles.sourceId, newsSources.id));

    // 构建查询条件
    const conditions = [eq(newsSources.isActive, true)];

    // 全文搜索条件
    const searchConditions = [
      ilike(articles.title, `%${query}%`),
      ilike(articles.summary, `%${query}%`),
      ilike(articles.content, `%${query}%`),
    ];

    // 检查标签匹配
    const tagCondition = sql`EXISTS (
      SELECT 1 FROM unnest(${articles.tags}) AS tag 
      WHERE tag ILIKE ${`%${query}%`}
    )`;
    searchConditions.push(tagCondition);

    conditions.push(or(...searchConditions));

    // 如果只查看订阅的内容
    if (subscribed) {
      const userSubscribedSources = await db
        .select({ sourceId: userSubscriptions.sourceId })
        .from(userSubscriptions)
        .where(
          and(
            eq(userSubscriptions.userId, session.user.id),
            eq(userSubscriptions.isActive, true)
          )
        );

      const subscribedSourceIds = userSubscribedSources.map(sub => sub.sourceId);
      
      if (subscribedSourceIds.length > 0) {
        conditions.push(inArray(articles.sourceId, subscribedSourceIds));
      } else {
        // 用户没有订阅任何源，返回空结果
        return NextResponse.json({
          articles: [],
          pagination: {
            page,
            limit,
            total: 0,
            totalPages: 0,
          },
          query,
          suggestions: [],
        });
      }
    }

    if (category) {
      conditions.push(eq(articles.category, category));
    }

    if (sourceId) {
      conditions.push(eq(articles.sourceId, sourceId));
    }

    // 应用查询条件
    if (conditions.length > 0) {
      searchQuery = searchQuery.where(and(...conditions));
    }

    // 排序
    switch (sortBy) {
      case 'date':
        searchQuery = searchQuery.orderBy(desc(articles.publishedAt), desc(articles.createdAt));
        break;
      case 'title':
        searchQuery = searchQuery.orderBy(articles.title);
        break;
      case 'relevance':
      default:
        searchQuery = searchQuery.orderBy(
          desc(sql`relevance_score`),
          desc(articles.publishedAt),
          desc(articles.createdAt)
        );
        break;
    }

    // 执行查询
    const searchResults = await searchQuery
      .limit(limit)
      .offset(offset);

    // 获取总数
    let countQuery = db
      .select({ count: sql<number>`count(*)` })
      .from(articles)
      .innerJoin(newsSources, eq(articles.sourceId, newsSources.id));

    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }

    const [{ count: total }] = await countQuery;

    // 高亮搜索结果
    const highlightedResults = searchResults.map(article => ({
      ...article,
      title: highlightText(article.title, query),
      summary: article.summary ? highlightText(article.summary, query) : undefined,
      content: article.content ? highlightText(article.content.substring(0, 500), query) : undefined,
    }));

    // 生成搜索建议
    const suggestions = await generateSearchSuggestions(query);

    return NextResponse.json({
      articles: highlightedResults,
      pagination: {
        page,
        limit,
        total: Number(total),
        totalPages: Math.ceil(Number(total) / limit),
      },
      query,
      suggestions,
    });
  } catch (error) {
    console.error('搜索失败:', error);
    return NextResponse.json(
      { error: '搜索失败' },
      { status: 500 }
    );
  }
}

/**
 * 高亮搜索关键词
 */
function highlightText(text: string, query: string): string {
  if (!text || !query) return text;
  
  const regex = new RegExp(`(${query})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * 生成搜索建议
 */
async function generateSearchSuggestions(query: string): Promise<string[]> {
  try {
    // 基于常见的IVD关键词生成建议
    const ivdKeywords = [
      'FDA批准', 'CE认证', '临床试验', '体外诊断', '医疗器械',
      '生物标志物', '诊断试剂', '分子诊断', '免疫诊断', 'POCT',
      '监管政策', '市场分析', '技术创新', '专利申请', '质量控制'
    ];

    const suggestions = ivdKeywords
      .filter(keyword => keyword.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 5);

    return suggestions;
  } catch (error) {
    console.error('生成搜索建议失败:', error);
    return [];
  }
}
