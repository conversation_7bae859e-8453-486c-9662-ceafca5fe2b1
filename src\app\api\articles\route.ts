import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { articles, newsSources, userSubscriptions } from '@/db/schema';
import { eq, and, or, ilike, desc, inArray } from 'drizzle-orm';

// GET /api/articles - 获取文章列表
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sourceId = searchParams.get('sourceId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const subscribed = searchParams.get('subscribed') === 'true';
    const offset = (page - 1) * limit;

    // 构建基础查询
    let query = db
      .select({
        id: articles.id,
        sourceId: articles.sourceId,
        title: articles.title,
        summary: articles.summary,
        author: articles.author,
        publishedAt: articles.publishedAt,
        tags: articles.tags,
        category: articles.category,
        url: articles.url,
        createdAt: articles.createdAt,
        source: {
          id: newsSources.id,
          name: newsSources.name,
          category: newsSources.category,
        },
      })
      .from(articles)
      .innerJoin(newsSources, eq(articles.sourceId, newsSources.id));

    // 构建查询条件
    const conditions = [eq(newsSources.isActive, true)];

    // 如果只查看订阅的内容
    if (subscribed) {
      const userSubscribedSources = await db
        .select({ sourceId: userSubscriptions.sourceId })
        .from(userSubscriptions)
        .where(
          and(
            eq(userSubscriptions.userId, session.user.id),
            eq(userSubscriptions.isActive, true)
          )
        );

      const subscribedSourceIds = userSubscribedSources.map(sub => sub.sourceId);
      
      if (subscribedSourceIds.length > 0) {
        conditions.push(inArray(articles.sourceId, subscribedSourceIds));
      } else {
        // 用户没有订阅任何源，返回空结果
        return NextResponse.json({
          articles: [],
          pagination: {
            page,
            limit,
            total: 0,
            totalPages: 0,
          },
        });
      }
    }

    if (category) {
      conditions.push(eq(articles.category, category));
    }

    if (sourceId) {
      conditions.push(eq(articles.sourceId, sourceId));
    }

    if (search) {
      conditions.push(
        or(
          ilike(articles.title, `%${search}%`),
          ilike(articles.summary, `%${search}%`)
        )
      );
    }

    // 应用查询条件
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // 执行查询
    const articlesResult = await query
      .limit(limit)
      .offset(offset)
      .orderBy(desc(articles.publishedAt), desc(articles.createdAt));

    // 获取总数
    let countQuery = db
      .select({ count: articles.id })
      .from(articles)
      .innerJoin(newsSources, eq(articles.sourceId, newsSources.id));

    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }

    const [{ count: total }] = await countQuery;

    return NextResponse.json({
      articles: articlesResult,
      pagination: {
        page,
        limit,
        total: Number(total),
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  } catch (error) {
    console.error('获取文章列表失败:', error);
    return NextResponse.json(
      { error: '获取文章列表失败' },
      { status: 500 }
    );
  }
}
