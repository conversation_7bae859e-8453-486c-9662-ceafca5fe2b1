import { NextRequest, NextResponse } from 'next/server';
import { registerSchema } from '@/lib/auth';
import { createUser } from '@/lib/auth';
import { db } from '@/db';
import { users } from '@/db/schema';
import { eq, or } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = registerSchema.parse(body);

    // Check if user already exists
    const existingUser = await db
      .select()
      .from(users)
      .where(
        or(
          eq(users.email, validatedData.email),
          validatedData.phone ? eq(users.phone, validatedData.phone) : undefined
        )
      )
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json(
        { error: '用户已存在' },
        { status: 400 }
      );
    }

    // Create new user
    const newUser = await createUser({
      email: validatedData.email,
      phone: validatedData.phone,
      password: validatedData.password,
      authProvider: 'email',
    });

    // Return user without password
    const { passwordHash, ...userWithoutPassword } = newUser;

    return NextResponse.json({
      message: '注册成功',
      user: userWithoutPassword,
    });
  } catch (error) {
    console.error('Registration error:', error);
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: '输入数据无效', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    );
  }
}
