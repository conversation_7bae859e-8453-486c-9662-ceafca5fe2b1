'use client';

import { useState } from 'react';
import { LoginForm } from '@/components/auth/login-form';
import { PhoneAuthForm } from '@/components/auth/phone-auth-form';

export default function SignInPage() {
  const [authMode, setAuthMode] = useState<'email' | 'phone'>('email');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {authMode === 'email' ? (
          <LoginForm
            onSwitchToRegister={() => window.location.href = '/auth/signup'}
            onSwitchToPhone={() => setAuthMode('phone')}
          />
        ) : (
          <PhoneAuthForm
            mode="login"
            onSwitchToEmail={() => setAuthMode('email')}
          />
        )}
      </div>
    </div>
  );
}
