import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { userSubscriptions, newsSources } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

const subscribeSourceSchema = z.object({
  sourceId: z.string().uuid('无效的新闻源ID'),
  categories: z.array(z.string()).min(1, '至少选择一个分类'),
});

const updateSubscriptionSchema = z.object({
  categories: z.array(z.string()).min(1, '至少选择一个分类'),
  isActive: z.boolean().optional(),
});

// GET /api/subscriptions/sources - 获取用户订阅的新闻源
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const subscriptions = await db
      .select({
        id: userSubscriptions.id,
        sourceId: userSubscriptions.sourceId,
        categories: userSubscriptions.categories,
        isActive: userSubscriptions.isActive,
        createdAt: userSubscriptions.createdAt,
        source: {
          id: newsSources.id,
          name: newsSources.name,
          category: newsSources.category,
          baseUrl: newsSources.baseUrl,
          updateFrequency: newsSources.updateFrequency,
        },
      })
      .from(userSubscriptions)
      .innerJoin(newsSources, eq(userSubscriptions.sourceId, newsSources.id))
      .where(
        and(
          eq(userSubscriptions.userId, session.user.id),
          eq(newsSources.isActive, true)
        )
      )
      .orderBy(userSubscriptions.createdAt);

    return NextResponse.json({ subscriptions });
  } catch (error) {
    console.error('获取用户订阅失败:', error);
    return NextResponse.json(
      { error: '获取用户订阅失败' },
      { status: 500 }
    );
  }
}

// POST /api/subscriptions/sources - 订阅新闻源
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = subscribeSourceSchema.parse(body);

    // 检查新闻源是否存在且活跃
    const source = await db
      .select()
      .from(newsSources)
      .where(
        and(
          eq(newsSources.id, validatedData.sourceId),
          eq(newsSources.isActive, true)
        )
      )
      .limit(1);

    if (!source[0]) {
      return NextResponse.json(
        { error: '新闻源不存在或已停用' },
        { status: 404 }
      );
    }

    // 检查是否已经订阅
    const existingSubscription = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.userId, session.user.id),
          eq(userSubscriptions.sourceId, validatedData.sourceId)
        )
      )
      .limit(1);

    if (existingSubscription[0]) {
      // 如果已存在但不活跃，则重新激活
      if (!existingSubscription[0].isActive) {
        const [updatedSubscription] = await db
          .update(userSubscriptions)
          .set({
            categories: validatedData.categories,
            isActive: true,
          })
          .where(eq(userSubscriptions.id, existingSubscription[0].id))
          .returning();

        return NextResponse.json({
          message: '重新订阅成功',
          subscription: updatedSubscription,
        });
      } else {
        return NextResponse.json(
          { error: '已经订阅该新闻源' },
          { status: 400 }
        );
      }
    }

    // 创建新订阅
    const [newSubscription] = await db
      .insert(userSubscriptions)
      .values({
        userId: session.user.id,
        sourceId: validatedData.sourceId,
        categories: validatedData.categories,
        isActive: true,
      })
      .returning();

    return NextResponse.json({
      message: '订阅成功',
      subscription: newSubscription,
    });
  } catch (error) {
    console.error('订阅新闻源失败:', error);
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: '输入数据无效', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '订阅新闻源失败' },
      { status: 500 }
    );
  }
}
