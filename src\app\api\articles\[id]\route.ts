import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { articles, newsSources, userReadArticles } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/articles/[id] - 获取文章详情
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const article = await db
      .select({
        id: articles.id,
        sourceId: articles.sourceId,
        title: articles.title,
        content: articles.content,
        summary: articles.summary,
        author: articles.author,
        publishedAt: articles.publishedAt,
        tags: articles.tags,
        category: articles.category,
        url: articles.url,
        createdAt: articles.createdAt,
        source: {
          id: newsSources.id,
          name: newsSources.name,
          category: newsSources.category,
          baseUrl: newsSources.baseUrl,
        },
      })
      .from(articles)
      .innerJoin(newsSources, eq(articles.sourceId, newsSources.id))
      .where(
        and(
          eq(articles.id, params.id),
          eq(newsSources.isActive, true)
        )
      )
      .limit(1);

    if (!article[0]) {
      return NextResponse.json(
        { error: '文章不存在' },
        { status: 404 }
      );
    }

    // 记录用户阅读记录
    try {
      await db
        .insert(userReadArticles)
        .values({
          userId: session.user.id,
          articleId: params.id,
        })
        .onConflictDoNothing(); // 如果已存在则忽略
    } catch (error) {
      // 忽略记录阅读历史的错误
      console.warn('记录阅读历史失败:', error);
    }

    return NextResponse.json({ article: article[0] });
  } catch (error) {
    console.error('获取文章详情失败:', error);
    return NextResponse.json(
      { error: '获取文章详情失败' },
      { status: 500 }
    );
  }
}
