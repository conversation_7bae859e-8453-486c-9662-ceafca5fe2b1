import Bull from 'bull';
import { ContentAggregator } from './content-aggregator';
import { db } from '@/db';
import { newsSources } from '@/db/schema';
import { eq } from 'drizzle-orm';

// 创建任务队列
const contentQueue = new Bull('content aggregation', {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
  },
});

// 内容聚合器实例
const aggregator = new ContentAggregator();

/**
 * 任务处理器
 */
contentQueue.process('aggregate-source', async (job) => {
  const { sourceId } = job.data;
  
  console.log(`开始处理新闻源: ${sourceId}`);
  
  try {
    // 获取新闻源信息
    const [source] = await db
      .select()
      .from(newsSources)
      .where(eq(newsSources.id, sourceId))
      .limit(1);

    if (!source || !source.isActive) {
      throw new Error(`新闻源 ${sourceId} 不存在或已停用`);
    }

    // 抓取并保存内容
    const sourceData = {
      ...source,
      apiConfig: source.apiConfig as any,
      createdAt: source.createdAt!,
    };

    const articles = await aggregator.fetchFromSource(sourceData);
    const savedArticles = await aggregator.saveArticles(articles);

    console.log(`新闻源 ${source.name} 处理完成，保存了 ${savedArticles.length} 篇文章`);
    
    return {
      sourceId,
      sourceName: source.name,
      articlesCount: savedArticles.length,
    };
  } catch (error) {
    console.error(`处理新闻源 ${sourceId} 失败:`, error);
    throw error;
  }
});

contentQueue.process('aggregate-all', async (job) => {
  console.log('开始聚合所有新闻源');
  
  try {
    const articles = await aggregator.aggregateAllSources();
    
    console.log(`聚合完成，总共保存了 ${articles.length} 篇文章`);
    
    return {
      totalArticles: articles.length,
      timestamp: new Date(),
    };
  } catch (error) {
    console.error('聚合所有新闻源失败:', error);
    throw error;
  }
});

/**
 * 调度器类
 */
export class ContentScheduler {
  /**
   * 启动调度器
   */
  static async start() {
    console.log('启动内容聚合调度器');

    // 获取所有活跃的新闻源
    const sources = await db
      .select()
      .from(newsSources)
      .where(eq(newsSources.isActive, true));

    // 为每个新闻源设置定时任务
    for (const source of sources) {
      await this.scheduleSourceAggregation(source.id, source.updateFrequency);
    }

    // 设置全量聚合任务（每天执行一次）
    await this.scheduleFullAggregation();

    console.log(`已为 ${sources.length} 个新闻源设置定时任务`);
  }

  /**
   * 为单个新闻源设置定时任务
   */
  static async scheduleSourceAggregation(sourceId: string, frequencyHours: number) {
    const jobId = `source-${sourceId}`;
    
    // 移除现有任务
    await contentQueue.removeRepeatable('aggregate-source', {
      repeat: { every: frequencyHours * 60 * 60 * 1000 },
      jobId,
    });

    // 添加新任务
    await contentQueue.add(
      'aggregate-source',
      { sourceId },
      {
        repeat: { every: frequencyHours * 60 * 60 * 1000 },
        jobId,
        removeOnComplete: 10,
        removeOnFail: 5,
      }
    );

    console.log(`已为新闻源 ${sourceId} 设置 ${frequencyHours} 小时的定时任务`);
  }

  /**
   * 设置全量聚合任务
   */
  static async scheduleFullAggregation() {
    const jobId = 'full-aggregation';
    
    // 移除现有任务
    await contentQueue.removeRepeatable('aggregate-all', {
      repeat: { cron: '0 2 * * *' }, // 每天凌晨2点
      jobId,
    });

    // 添加新任务
    await contentQueue.add(
      'aggregate-all',
      {},
      {
        repeat: { cron: '0 2 * * *' }, // 每天凌晨2点
        jobId,
        removeOnComplete: 5,
        removeOnFail: 3,
      }
    );

    console.log('已设置全量聚合任务（每天凌晨2点执行）');
  }

  /**
   * 手动触发单个新闻源聚合
   */
  static async triggerSourceAggregation(sourceId: string) {
    const job = await contentQueue.add('aggregate-source', { sourceId }, {
      priority: 1,
      removeOnComplete: 10,
      removeOnFail: 5,
    });

    return job;
  }

  /**
   * 手动触发全量聚合
   */
  static async triggerFullAggregation() {
    const job = await contentQueue.add('aggregate-all', {}, {
      priority: 1,
      removeOnComplete: 10,
      removeOnFail: 5,
    });

    return job;
  }

  /**
   * 获取队列状态
   */
  static async getQueueStatus() {
    const waiting = await contentQueue.getWaiting();
    const active = await contentQueue.getActive();
    const completed = await contentQueue.getCompleted();
    const failed = await contentQueue.getFailed();

    return {
      waiting: waiting.length,
      active: active.length,
      completed: completed.length,
      failed: failed.length,
    };
  }

  /**
   * 清理队列
   */
  static async cleanQueue() {
    await contentQueue.clean(24 * 60 * 60 * 1000, 'completed'); // 清理24小时前的已完成任务
    await contentQueue.clean(7 * 24 * 60 * 60 * 1000, 'failed'); // 清理7天前的失败任务
  }

  /**
   * 停止调度器
   */
  static async stop() {
    await contentQueue.close();
    console.log('内容聚合调度器已停止');
  }
}

// 错误处理
contentQueue.on('error', (error) => {
  console.error('队列错误:', error);
});

contentQueue.on('failed', (job, error) => {
  console.error(`任务 ${job.id} 失败:`, error);
});

contentQueue.on('completed', (job, result) => {
  console.log(`任务 ${job.id} 完成:`, result);
});

export { contentQueue };
