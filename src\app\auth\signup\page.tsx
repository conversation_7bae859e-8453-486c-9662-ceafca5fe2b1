'use client';

import { useState } from 'react';
import { RegisterForm } from '@/components/auth/register-form';
import { PhoneAuthForm } from '@/components/auth/phone-auth-form';

export default function SignUpPage() {
  const [authMode, setAuthMode] = useState<'email' | 'phone'>('email');

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {authMode === 'email' ? (
          <RegisterForm
            onSwitchToLogin={() => window.location.href = '/auth/signin'}
            onSwitchToPhone={() => setAuthMode('phone')}
          />
        ) : (
          <PhoneAuthForm
            mode="register"
            onSwitchToEmail={() => setAuthMode('email')}
          />
        )}
      </div>
    </div>
  );
}
