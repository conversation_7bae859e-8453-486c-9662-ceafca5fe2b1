import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { userSubscriptions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { z } from 'zod';

const updateSubscriptionSchema = z.object({
  categories: z.array(z.string()).min(1, '至少选择一个分类').optional(),
  isActive: z.boolean().optional(),
});

// PUT /api/subscriptions/sources/[id] - 更新订阅设置
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateSubscriptionSchema.parse(body);

    // 检查订阅是否存在且属于当前用户
    const existingSubscription = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.id, params.id),
          eq(userSubscriptions.userId, session.user.id)
        )
      )
      .limit(1);

    if (!existingSubscription[0]) {
      return NextResponse.json(
        { error: '订阅不存在' },
        { status: 404 }
      );
    }

    const [updatedSubscription] = await db
      .update(userSubscriptions)
      .set(validatedData)
      .where(eq(userSubscriptions.id, params.id))
      .returning();

    return NextResponse.json({
      message: '订阅设置更新成功',
      subscription: updatedSubscription,
    });
  } catch (error) {
    console.error('更新订阅设置失败:', error);
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: '输入数据无效', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '更新订阅设置失败' },
      { status: 500 }
    );
  }
}

// DELETE /api/subscriptions/sources/[id] - 取消订阅
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 检查订阅是否存在且属于当前用户
    const existingSubscription = await db
      .select()
      .from(userSubscriptions)
      .where(
        and(
          eq(userSubscriptions.id, params.id),
          eq(userSubscriptions.userId, session.user.id)
        )
      )
      .limit(1);

    if (!existingSubscription[0]) {
      return NextResponse.json(
        { error: '订阅不存在' },
        { status: 404 }
      );
    }

    // 软删除：设置为不活跃状态
    await db
      .update(userSubscriptions)
      .set({ isActive: false })
      .where(eq(userSubscriptions.id, params.id));

    return NextResponse.json({
      message: '取消订阅成功',
    });
  } catch (error) {
    console.error('取消订阅失败:', error);
    return NextResponse.json(
      { error: '取消订阅失败' },
      { status: 500 }
    );
  }
}
