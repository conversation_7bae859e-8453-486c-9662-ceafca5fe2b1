import Redis from 'redis';

export interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
  keyPrefix?: string;
}

export class CacheService {
  private client: Redis.RedisClientType;
  private keyPrefix: string;

  constructor(config: Partial<CacheConfig> = {}) {
    const defaultConfig: CacheConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      keyPrefix: 'ivd_news:',
    };

    const finalConfig = { ...defaultConfig, ...config };
    this.keyPrefix = finalConfig.keyPrefix!;

    this.client = Redis.createClient({
      socket: {
        host: finalConfig.host,
        port: finalConfig.port,
      },
      password: finalConfig.password,
      database: finalConfig.db,
    });

    this.client.on('error', (error) => {
      console.error('Redis连接错误:', error);
    });

    this.client.on('connect', () => {
      console.log('Redis连接成功');
    });

    this.connect();
  }

  /**
   * 连接到Redis
   */
  private async connect() {
    try {
      await this.client.connect();
    } catch (error) {
      console.error('Redis连接失败:', error);
    }
  }

  /**
   * 生成带前缀的键名
   */
  private getKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      const redisKey = this.getKey(key);

      if (ttl) {
        await this.client.setEx(redisKey, ttl, serializedValue);
      } else {
        await this.client.set(redisKey, serializedValue);
      }
    } catch (error) {
      console.error('设置缓存失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存
   */
  async get<T = any>(key: string): Promise<T | null> {
    try {
      const redisKey = this.getKey(key);
      const value = await this.client.get(redisKey);
      
      if (value === null) {
        return null;
      }

      return JSON.parse(value) as T;
    } catch (error) {
      console.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    try {
      const redisKey = this.getKey(key);
      await this.client.del(redisKey);
    } catch (error) {
      console.error('删除缓存失败:', error);
      throw error;
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const redisKey = this.getKey(key);
      const result = await this.client.exists(redisKey);
      return result === 1;
    } catch (error) {
      console.error('检查缓存存在性失败:', error);
      return false;
    }
  }

  /**
   * 设置过期时间
   */
  async expire(key: string, ttl: number): Promise<void> {
    try {
      const redisKey = this.getKey(key);
      await this.client.expire(redisKey, ttl);
    } catch (error) {
      console.error('设置过期时间失败:', error);
      throw error;
    }
  }

  /**
   * 获取剩余过期时间
   */
  async ttl(key: string): Promise<number> {
    try {
      const redisKey = this.getKey(key);
      return await this.client.ttl(redisKey);
    } catch (error) {
      console.error('获取过期时间失败:', error);
      return -1;
    }
  }

  /**
   * 批量设置
   */
  async mset(keyValuePairs: Record<string, any>, ttl?: number): Promise<void> {
    try {
      const pipeline = this.client.multi();
      
      for (const [key, value] of Object.entries(keyValuePairs)) {
        const redisKey = this.getKey(key);
        const serializedValue = JSON.stringify(value);
        
        if (ttl) {
          pipeline.setEx(redisKey, ttl, serializedValue);
        } else {
          pipeline.set(redisKey, serializedValue);
        }
      }

      await pipeline.exec();
    } catch (error) {
      console.error('批量设置缓存失败:', error);
      throw error;
    }
  }

  /**
   * 批量获取
   */
  async mget<T = any>(keys: string[]): Promise<(T | null)[]> {
    try {
      const redisKeys = keys.map(key => this.getKey(key));
      const values = await this.client.mGet(redisKeys);
      
      return values.map(value => {
        if (value === null) return null;
        try {
          return JSON.parse(value) as T;
        } catch {
          return null;
        }
      });
    } catch (error) {
      console.error('批量获取缓存失败:', error);
      return keys.map(() => null);
    }
  }

  /**
   * 增加数值
   */
  async incr(key: string, increment = 1): Promise<number> {
    try {
      const redisKey = this.getKey(key);
      return await this.client.incrBy(redisKey, increment);
    } catch (error) {
      console.error('增加数值失败:', error);
      throw error;
    }
  }

  /**
   * 减少数值
   */
  async decr(key: string, decrement = 1): Promise<number> {
    try {
      const redisKey = this.getKey(key);
      return await this.client.decrBy(redisKey, decrement);
    } catch (error) {
      console.error('减少数值失败:', error);
      throw error;
    }
  }

  /**
   * 列表操作 - 左推入
   */
  async lpush(key: string, ...values: any[]): Promise<number> {
    try {
      const redisKey = this.getKey(key);
      const serializedValues = values.map(v => JSON.stringify(v));
      return await this.client.lPush(redisKey, serializedValues);
    } catch (error) {
      console.error('列表左推入失败:', error);
      throw error;
    }
  }

  /**
   * 列表操作 - 右推入
   */
  async rpush(key: string, ...values: any[]): Promise<number> {
    try {
      const redisKey = this.getKey(key);
      const serializedValues = values.map(v => JSON.stringify(v));
      return await this.client.rPush(redisKey, serializedValues);
    } catch (error) {
      console.error('列表右推入失败:', error);
      throw error;
    }
  }

  /**
   * 列表操作 - 获取范围
   */
  async lrange<T = any>(key: string, start: number, stop: number): Promise<T[]> {
    try {
      const redisKey = this.getKey(key);
      const values = await this.client.lRange(redisKey, start, stop);
      
      return values.map(value => {
        try {
          return JSON.parse(value) as T;
        } catch {
          return value as T;
        }
      });
    } catch (error) {
      console.error('获取列表范围失败:', error);
      return [];
    }
  }

  /**
   * 清除所有缓存
   */
  async flushAll(): Promise<void> {
    try {
      await this.client.flushAll();
    } catch (error) {
      console.error('清除所有缓存失败:', error);
      throw error;
    }
  }

  /**
   * 关闭连接
   */
  async disconnect(): Promise<void> {
    try {
      await this.client.disconnect();
      console.log('Redis连接已关闭');
    } catch (error) {
      console.error('关闭Redis连接失败:', error);
    }
  }
}

// 创建全局缓存服务实例
export const cacheService = new CacheService();
