import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { wsManager } from '@/services/websocket';

// GET /api/websocket/status - 获取WebSocket状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const status = {
      onlineUsers: wsManager.getOnlineUsersCount(),
      totalConnections: wsManager.getTotalConnections(),
      timestamp: new Date(),
    };

    return NextResponse.json({ status });
  } catch (error) {
    console.error('获取WebSocket状态失败:', error);
    return NextResponse.json(
      { error: '获取WebSocket状态失败' },
      { status: 500 }
    );
  }
}
