import { db } from '@/db';
import { newsSources, articles } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { NewsSource, Article } from '@/types';
import { cacheService } from './cache';
import { wsManager } from './websocket';

export interface ContentAggregatorConfig {
  maxRetries: number;
  retryDelay: number;
  timeout: number;
}

export class ContentAggregator {
  private config: ContentAggregatorConfig;

  constructor(config: Partial<ContentAggregatorConfig> = {}) {
    this.config = {
      maxRetries: 3,
      retryDelay: 1000,
      timeout: 30000,
      ...config,
    };
  }

  /**
   * 获取所有活跃的新闻源
   */
  async getActiveSources(): Promise<NewsSource[]> {
    const cacheKey = 'active_sources';

    // 尝试从缓存获取
    const cachedSources = await cacheService.get<NewsSource[]>(cacheKey);
    if (cachedSources) {
      return cachedSources;
    }

    const sources = await db
      .select()
      .from(newsSources)
      .where(eq(newsSources.isActive, true));

    const mappedSources = sources.map(source => ({
      ...source,
      apiConfig: source.apiConfig as any,
      createdAt: source.createdAt!,
    }));

    // 缓存5分钟
    await cacheService.set(cacheKey, mappedSources, 300);

    return mappedSources;
  }

  /**
   * 从单个新闻源抓取内容
   */
  async fetchFromSource(source: NewsSource): Promise<Article[]> {
    console.log(`开始抓取新闻源: ${source.name}`);

    try {
      switch (source.apiConfig.type) {
        case 'rss':
          return await this.fetchFromRSS(source);
        case 'api':
          return await this.fetchFromAPI(source);
        case 'web_scraping':
          return await this.fetchFromWebScraping(source);
        default:
          throw new Error(`不支持的内容源类型: ${source.apiConfig.type}`);
      }
    } catch (error) {
      console.error(`抓取新闻源 ${source.name} 失败:`, error);
      return [];
    }
  }

  /**
   * 从RSS源抓取内容
   */
  private async fetchFromRSS(source: NewsSource): Promise<Article[]> {
    if (!source.apiConfig.feedUrl) {
      throw new Error('RSS源缺少feedUrl配置');
    }

    const response = await this.fetchWithRetry(source.apiConfig.feedUrl);
    const xmlText = await response.text();
    
    // 简单的RSS解析（生产环境建议使用专门的RSS解析库）
    const articles: Article[] = [];
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
    
    const items = xmlDoc.querySelectorAll('item');
    
    for (const item of items) {
      const title = item.querySelector('title')?.textContent?.trim();
      const link = item.querySelector('link')?.textContent?.trim();
      const description = item.querySelector('description')?.textContent?.trim();
      const pubDate = item.querySelector('pubDate')?.textContent?.trim();
      const author = item.querySelector('author')?.textContent?.trim();

      if (title && link) {
        articles.push({
          id: '', // 将在保存时生成
          sourceId: source.id,
          title,
          content: description || '',
          summary: this.generateSummary(description || ''),
          author: author || undefined,
          publishedAt: pubDate ? new Date(pubDate) : undefined,
          tags: this.extractTags(title, description || ''),
          category: source.category,
          url: link,
          createdAt: new Date(),
        });
      }
    }

    return articles;
  }

  /**
   * 从API抓取内容
   */
  private async fetchFromAPI(source: NewsSource): Promise<Article[]> {
    if (!source.apiConfig.endpoint) {
      throw new Error('API源缺少endpoint配置');
    }

    const url = new URL(source.apiConfig.endpoint, source.baseUrl);
    
    if (source.apiConfig.apiKey) {
      url.searchParams.set('apiKey', source.apiConfig.apiKey);
    }

    const response = await this.fetchWithRetry(url.toString());
    const data = await response.json();

    // 根据不同API的响应格式进行解析
    // 这里提供一个通用的解析逻辑
    const articles: Article[] = [];
    const items = Array.isArray(data) ? data : data.articles || data.items || [];

    for (const item of items) {
      if (item.title) {
        articles.push({
          id: '',
          sourceId: source.id,
          title: item.title,
          content: item.content || item.description || '',
          summary: this.generateSummary(item.content || item.description || ''),
          author: item.author || undefined,
          publishedAt: item.publishedAt || item.pubDate ? new Date(item.publishedAt || item.pubDate) : undefined,
          tags: this.extractTags(item.title, item.content || item.description || ''),
          category: source.category,
          url: item.url || item.link || '',
          createdAt: new Date(),
        });
      }
    }

    return articles;
  }

  /**
   * 从网页抓取内容
   */
  private async fetchFromWebScraping(source: NewsSource): Promise<Article[]> {
    // 网页抓取需要更复杂的实现，这里提供基础框架
    // 生产环境建议使用Puppeteer或Playwright
    
    const response = await this.fetchWithRetry(source.baseUrl);
    const html = await response.text();
    
    // 简单的HTML解析示例
    const articles: Article[] = [];
    
    // 这里需要根据具体网站的结构来实现
    // 使用source.apiConfig.selectors中的选择器
    
    console.log(`网页抓取功能需要针对 ${source.name} 进行具体实现`);
    
    return articles;
  }

  /**
   * 带重试的网络请求
   */
  private async fetchWithRetry(url: string, retries = 0): Promise<Response> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'User-Agent': 'IVD-Newsletter-Bot/1.0',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      if (retries < this.config.maxRetries) {
        console.log(`请求失败，${this.config.retryDelay}ms后重试 (${retries + 1}/${this.config.maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        return this.fetchWithRetry(url, retries + 1);
      }
      throw error;
    }
  }

  /**
   * 生成文章摘要
   */
  private generateSummary(content: string, maxLength = 200): string {
    if (!content) return '';
    
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '');
    
    if (textContent.length <= maxLength) {
      return textContent;
    }
    
    return textContent.substring(0, maxLength).trim() + '...';
  }

  /**
   * 从标题和内容中提取标签
   */
  private extractTags(title: string, content: string): string[] {
    const text = `${title} ${content}`.toLowerCase();
    const tags: string[] = [];

    // IVD相关关键词
    const ivdKeywords = [
      'ivd', '体外诊断', '医疗器械', '诊断试剂', '生物标志物',
      'biomarker', 'diagnostic', 'medical device', 'reagent',
      'fda', 'ce', '认证', 'approval', '批准',
      '临床试验', 'clinical trial', '监管', 'regulatory',
      '市场', 'market', '专利', 'patent', '技术', 'technology'
    ];

    for (const keyword of ivdKeywords) {
      if (text.includes(keyword)) {
        tags.push(keyword);
      }
    }

    return [...new Set(tags)]; // 去重
  }

  /**
   * 检查文章是否重复
   */
  async isDuplicateArticle(article: Partial<Article>): Promise<boolean> {
    if (!article.title || !article.sourceId) return false;

    const existing = await db
      .select()
      .from(articles)
      .where(eq(articles.title, article.title))
      .limit(1);

    return existing.length > 0;
  }

  /**
   * 保存文章到数据库
   */
  async saveArticles(articlesToSave: Omit<Article, 'id'>[]): Promise<Article[]> {
    if (articlesToSave.length === 0) return [];

    // 过滤重复文章
    const uniqueArticles = [];
    for (const article of articlesToSave) {
      const isDuplicate = await this.isDuplicateArticle(article);
      if (!isDuplicate) {
        uniqueArticles.push(article);
      }
    }

    if (uniqueArticles.length === 0) {
      console.log('没有新文章需要保存');
      return [];
    }

    const savedArticles = await db
      .insert(articles)
      .values(uniqueArticles)
      .returning();

    console.log(`保存了 ${savedArticles.length} 篇新文章`);

    // 发送实时通知
    for (const article of savedArticles) {
      wsManager.broadcastNewArticle(article);

      // 缓存最新文章
      const cacheKey = `latest_articles:${article.category}`;
      await cacheService.lpush(cacheKey, article);

      // 保持最新100篇文章的缓存
      const cachedArticles = await cacheService.lrange(cacheKey, 0, 99);
      if (cachedArticles.length > 100) {
        await cacheService.del(cacheKey);
        await cacheService.rpush(cacheKey, ...cachedArticles.slice(0, 100));
      }
    }

    return savedArticles;
  }

  /**
   * 聚合所有活跃源的内容
   */
  async aggregateAllSources(): Promise<Article[]> {
    const sources = await this.getActiveSources();
    const allArticles: Article[] = [];

    for (const source of sources) {
      try {
        const sourceArticles = await this.fetchFromSource(source);
        const savedArticles = await this.saveArticles(sourceArticles);
        allArticles.push(...savedArticles);
      } catch (error) {
        console.error(`处理新闻源 ${source.name} 时出错:`, error);
      }
    }

    return allArticles;
  }
}
