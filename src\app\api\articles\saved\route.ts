import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { articles, newsSources, userSavedArticles } from '@/db/schema';
import { eq, and, desc } from 'drizzle-orm';

// GET /api/articles/saved - 获取用户保存的文章
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    const savedArticles = await db
      .select({
        id: articles.id,
        sourceId: articles.sourceId,
        title: articles.title,
        summary: articles.summary,
        author: articles.author,
        publishedAt: articles.publishedAt,
        tags: articles.tags,
        category: articles.category,
        url: articles.url,
        createdAt: articles.createdAt,
        savedAt: userSavedArticles.createdAt,
        source: {
          id: newsSources.id,
          name: newsSources.name,
          category: newsSources.category,
        },
      })
      .from(userSavedArticles)
      .innerJoin(articles, eq(userSavedArticles.articleId, articles.id))
      .innerJoin(newsSources, eq(articles.sourceId, newsSources.id))
      .where(
        and(
          eq(userSavedArticles.userId, session.user.id),
          eq(newsSources.isActive, true)
        )
      )
      .limit(limit)
      .offset(offset)
      .orderBy(desc(userSavedArticles.createdAt));

    // 获取总数
    const [{ count: total }] = await db
      .select({ count: userSavedArticles.id })
      .from(userSavedArticles)
      .innerJoin(articles, eq(userSavedArticles.articleId, articles.id))
      .innerJoin(newsSources, eq(articles.sourceId, newsSources.id))
      .where(
        and(
          eq(userSavedArticles.userId, session.user.id),
          eq(newsSources.isActive, true)
        )
      );

    return NextResponse.json({
      articles: savedArticles,
      pagination: {
        page,
        limit,
        total: Number(total),
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  } catch (error) {
    console.error('获取保存的文章失败:', error);
    return NextResponse.json(
      { error: '获取保存的文章失败' },
      { status: 500 }
    );
  }
}
