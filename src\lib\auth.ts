import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { db } from '@/db';
import { users } from '@/db/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';
import { z } from 'zod';

// Validation schemas
export const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少需要6个字符'),
});

export const registerSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少需要6个字符'),
  confirmPassword: z.string(),
  phone: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: '密码确认不匹配',
  path: ['confirmPassword'],
});

export const phoneRegisterSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号'),
  code: z.string().length(6, '验证码必须是6位数字'),
  password: z.string().min(6, '密码至少需要6个字符'),
});

// NextAuth configuration
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: 'email',
      name: 'Email',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const user = await db
            .select()
            .from(users)
            .where(eq(users.email, credentials.email))
            .limit(1);

          if (!user[0] || !user[0].passwordHash) {
            return null;
          }

          const isPasswordValid = await bcrypt.compare(
            credentials.password,
            user[0].passwordHash
          );

          if (!isPasswordValid) {
            return null;
          }

          return {
            id: user[0].id,
            email: user[0].email,
            phone: user[0].phone,
            authProvider: user[0].authProvider,
          };
        } catch (error) {
          console.error('Authentication error:', error);
          return null;
        }
      },
    }),
    CredentialsProvider({
      id: 'phone',
      name: 'Phone',
      credentials: {
        phone: { label: 'Phone', type: 'tel' },
        code: { label: 'Verification Code', type: 'text' },
      },
      async authorize(credentials) {
        if (!credentials?.phone || !credentials?.code) {
          return null;
        }

        try {
          // TODO: Verify SMS code with third-party service
          // For now, we'll use a simple check
          if (credentials.code !== '123456') {
            return null;
          }

          const user = await db
            .select()
            .from(users)
            .where(eq(users.phone, credentials.phone))
            .limit(1);

          if (!user[0]) {
            return null;
          }

          return {
            id: user[0].id,
            email: user[0].email,
            phone: user[0].phone,
            authProvider: user[0].authProvider,
          };
        } catch (error) {
          console.error('Phone authentication error:', error);
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.phone = user.phone;
        token.authProvider = user.authProvider;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.phone = token.phone as string;
        session.user.authProvider = token.authProvider as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
  },
};

// Utility functions
export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12);
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword);
}

export async function createUser(data: {
  email?: string;
  phone?: string;
  password: string;
  authProvider?: string;
}) {
  const hashedPassword = await hashPassword(data.password);
  
  const newUser = await db
    .insert(users)
    .values({
      email: data.email,
      phone: data.phone,
      passwordHash: hashedPassword,
      authProvider: data.authProvider || 'email',
      profile: {},
      subscriptionPreferences: {
        categories: ['regulatory', 'industry', 'market'],
        frequency: 'daily',
        emailNotifications: true,
      },
    })
    .returning();

  return newUser[0];
}

// SMS verification (placeholder implementation)
export async function sendSMSVerification(phone: string): Promise<boolean> {
  // TODO: Integrate with SMS service provider
  console.log(`Sending SMS verification to ${phone}`);
  return true;
}

export async function verifySMSCode(phone: string, code: string): Promise<boolean> {
  // TODO: Verify with SMS service provider
  // For development, accept '123456' as valid code
  return code === '123456';
}
