'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'

export function Header() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 hidden md:flex">
          <Link className="mr-6 flex items-center space-x-2" href="/">
            <span className="hidden font-bold sm:inline-block">
              IVD资讯聚合平台
            </span>
          </Link>
          <nav className="flex items-center space-x-6 text-sm font-medium">
            <Link
              className="transition-colors hover:text-foreground/80 text-foreground/60"
              href="/dashboard"
            >
              仪表板
            </Link>
            <Link
              className="transition-colors hover:text-foreground/80 text-foreground/60"
              href="/sources"
            >
              信息源
            </Link>
            <Link
              className="transition-colors hover:text-foreground/80 text-foreground/60"
              href="/subscription"
            >
              订阅管理
            </Link>
          </nav>
        </div>
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            {/* Search component will go here */}
          </div>
          <nav className="flex items-center space-x-2">
            <Button variant="ghost" size="sm">
              登录
            </Button>
            <Button size="sm">注册</Button>
          </nav>
        </div>
      </div>
    </header>
  )
}