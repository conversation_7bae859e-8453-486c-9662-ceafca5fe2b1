import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/db';
import { newsSources } from '@/db/schema';
import { eq, and, or, ilike } from 'drizzle-orm';
import { z } from 'zod';

const createSourceSchema = z.object({
  name: z.string().min(1, '新闻源名称不能为空'),
  category: z.enum(['regulatory', 'industry', 'market', 'clinical', 'patents', 'news']),
  baseUrl: z.string().url('请输入有效的URL'),
  apiConfig: z.object({
    type: z.enum(['rss', 'api', 'web_scraping']),
    feedUrl: z.string().url().optional(),
    endpoint: z.string().optional(),
    apiKey: z.string().optional(),
    selectors: z.record(z.string()).optional(),
  }),
  updateFrequency: z.number().min(1, '更新频率必须大于0'),
});

// GET /api/sources - 获取新闻源列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    let query = db.select().from(newsSources);

    // 构建查询条件
    const conditions = [];
    
    if (category) {
      conditions.push(eq(newsSources.category, category as any));
    }
    
    if (search) {
      conditions.push(
        or(
          ilike(newsSources.name, `%${search}%`),
          ilike(newsSources.baseUrl, `%${search}%`)
        )
      );
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    const sources = await query
      .limit(limit)
      .offset(offset)
      .orderBy(newsSources.createdAt);

    // 获取总数
    const totalQuery = db.select({ count: newsSources.id }).from(newsSources);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    return NextResponse.json({
      sources,
      pagination: {
        page,
        limit,
        total: Number(total),
        totalPages: Math.ceil(Number(total) / limit),
      },
    });
  } catch (error) {
    console.error('获取新闻源列表失败:', error);
    return NextResponse.json(
      { error: '获取新闻源列表失败' },
      { status: 500 }
    );
  }
}

// POST /api/sources - 创建新闻源（管理员功能）
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // TODO: 检查管理员权限
    // if (!isAdmin(session.user.id)) {
    //   return NextResponse.json(
    //     { error: '权限不足' },
    //     { status: 403 }
    //   );
    // }

    const body = await request.json();
    const validatedData = createSourceSchema.parse(body);

    // 检查是否已存在相同的新闻源
    const existingSource = await db
      .select()
      .from(newsSources)
      .where(
        or(
          eq(newsSources.name, validatedData.name),
          eq(newsSources.baseUrl, validatedData.baseUrl)
        )
      )
      .limit(1);

    if (existingSource.length > 0) {
      return NextResponse.json(
        { error: '新闻源已存在' },
        { status: 400 }
      );
    }

    const [newSource] = await db
      .insert(newsSources)
      .values({
        ...validatedData,
        isActive: true,
      })
      .returning();

    return NextResponse.json({
      message: '新闻源创建成功',
      source: newSource,
    });
  } catch (error) {
    console.error('创建新闻源失败:', error);
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: '输入数据无效', details: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: '创建新闻源失败' },
      { status: 500 }
    );
  }
}
