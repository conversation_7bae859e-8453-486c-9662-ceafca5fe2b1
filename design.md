# 设计文档

## 概述

IVD（体外诊断）与医疗技术行业资讯聚合与订阅管理系统是一个面向行业专业人士的综合信息平台。系统通过聚合多个权威信息源，提供个性化的新闻订阅服务，并集成支付功能以支持高级功能。

核心设计原则：
- **可扩展性**：支持动态添加新的信息源和内容类型
- **个性化**：基于用户偏好提供定制化内容体验
- **实时性**：确保关键行业信息的及时传递
- **安全性**：采用企业级身份认证和数据保护
- **性能优化**：利用CDN和缓存策略提供全球化高性能访问

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Web应用 - Next.js]
        B[移动端响应式界面]
    end
    
    subgraph "API网关层"
        C[API Gateway]
        D[认证中间件]
    end
    
    subgraph "应用服务层"
        E[用户服务]
        F[内容聚合服务]
        G[订阅管理服务]
        H[支付服务]
        I[通知服务]
    end
    
    subgraph "数据层"
        J[用户数据库]
        K[内容数据库]
        L[订阅数据库]
        M[Redis缓存]
    end
    
    subgraph "外部集成"
        N[腾讯云CIAM]
        O[微信支付]
        P[支付宝]
        Q[新闻源APIs]
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    D --> F
    D --> G
    D --> H
    D --> I
    
    E --> J
    F --> K
    G --> L
    E --> M
    F --> M
    
    E --> N
    H --> O
    H --> P
    F --> Q
```

### 技术栈选择

**前端技术栈**：
- Next.js 14 (App Router) - 提供SSR/SSG和优化的性能
- TypeScript - 类型安全和开发体验
- Tailwind CSS + Shadcn/ui - 响应式设计和组件库
- React Query - 数据获取和状态管理

**后端技术栈**：
- Node.js + Express/Fastify - API服务
- PostgreSQL - 主数据库
- Redis - 缓存和会话存储
- Bull Queue - 任务队列处理

**部署和基础设施**：
- 腾讯云EdgeOne Pages - 前端部署和CDN
- 腾讯云CVM - 后端服务部署
- 腾讯云CIAM - 身份认证服务

## 组件和接口

### 核心组件

#### 1. 用户认证组件 (AuthComponent)
```typescript
interface AuthService {
  // 多种认证方式支持
  registerWithEmail(email: string, password: string): Promise<User>
  registerWithPhone(phone: string, code: string): Promise<User>
  socialLogin(provider: 'wechat' | 'qq', token: string): Promise<User>
  passwordlessLogin(email: string): Promise<void>
  
  // 腾讯云CIAM集成
  ciamAuthenticate(token: string): Promise<User>
  resetPassword(email: string): Promise<void>
}
```

#### 2. 内容聚合组件 (ContentAggregator)
```typescript
interface ContentAggregator {
  // 信息源管理
  getAvailableSources(): Promise<NewsSource[]>
  subscribeToSource(userId: string, sourceId: string, categories: string[]): Promise<void>
  
  // 内容聚合
  aggregateContent(userId: string): Promise<Article[]>
  searchContent(query: string, filters: SearchFilters): Promise<Article[]>
  
  // 实时更新
  setupRealTimeUpdates(userId: string): WebSocket
}

interface NewsSource {
  id: string
  name: string
  category: 'regulatory' | 'industry' | 'news' | 'market'
  updateFrequency: string
  contentTypes: string[]
  geoFocus: string[]
}
```

#### 3. 订阅管理组件 (SubscriptionManager)
```typescript
interface SubscriptionManager {
  // 订阅计划管理
  getSubscriptionPlans(): Promise<SubscriptionPlan[]>
  createSubscription(userId: string, planId: string): Promise<Subscription>
  
  // 支付集成
  processWeChatPayment(amount: number, userId: string): Promise<PaymentResult>
  processAlipayPayment(amount: number, userId: string): Promise<PaymentResult>
  
  // 循环支付
  setupRecurringPayment(subscriptionId: string): Promise<void>
  handlePaymentFailure(subscriptionId: string): Promise<void>
}
```

#### 4. 通知系统组件 (NotificationSystem)
```typescript
interface NotificationSystem {
  // 通知配置
  updateNotificationPreferences(userId: string, preferences: NotificationPrefs): Promise<void>
  
  // 通知发送
  sendRegulatoryAlert(content: Article, targetUsers: string[]): Promise<void>
  sendMarketUpdate(content: Article, targetUsers: string[]): Promise<void>
  
  // 通知历史
  getNotificationHistory(userId: string): Promise<Notification[]>
}
```

### API接口设计

#### 认证相关API
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
POST /api/auth/reset-password
```

#### 内容相关API
```
GET /api/sources
POST /api/subscriptions/sources
GET /api/dashboard/feed
GET /api/content/search
POST /api/content/save
PUT /api/content/mark-read
```

#### 订阅和支付API
```
GET /api/subscription/plans
POST /api/subscription/create
POST /api/payment/wechat
POST /api/payment/alipay
GET /api/subscription/status
PUT /api/subscription/cancel
```

## 数据模型

### 用户数据模型
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    auth_provider VARCHAR(50),
    ciam_user_id VARCHAR(255),
    profile JSONB,
    subscription_preferences JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE user_sessions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    token_hash VARCHAR(255),
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 内容数据模型
```sql
CREATE TABLE news_sources (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(50),
    base_url VARCHAR(500),
    api_config JSONB,
    update_frequency INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE articles (
    id UUID PRIMARY KEY,
    source_id UUID REFERENCES news_sources(id),
    title VARCHAR(500) NOT NULL,
    content TEXT,
    summary TEXT,
    author VARCHAR(255),
    published_at TIMESTAMP,
    tags TEXT[],
    category VARCHAR(50),
    url VARCHAR(500),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE user_subscriptions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    source_id UUID REFERENCES news_sources(id),
    categories TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 订阅和支付数据模型
```sql
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2),
    billing_cycle VARCHAR(20), -- monthly, yearly
    features JSONB,
    is_active BOOLEAN DEFAULT true
);

CREATE TABLE user_subscriptions_paid (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(20), -- active, cancelled, expired
    current_period_start TIMESTAMP,
    current_period_end TIMESTAMP,
    payment_method VARCHAR(20), -- wechat, alipay
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE payments (
    id UUID PRIMARY KEY,
    subscription_id UUID REFERENCES user_subscriptions_paid(id),
    amount DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'CNY',
    payment_method VARCHAR(20),
    transaction_id VARCHAR(255),
    status VARCHAR(20), -- pending, completed, failed
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 错误处理

### 错误分类和处理策略

#### 1. 认证错误
- **无效凭据**：返回401状态码，提供重新登录选项
- **会话过期**：自动刷新token或重定向到登录页面
- **CIAM服务不可用**：降级到本地认证或显示维护提示

#### 2. 内容聚合错误
- **信息源不可用**：记录错误日志，跳过该源继续聚合其他源
- **解析失败**：使用备用解析策略或人工审核队列
- **API限流**：实现指数退避重试机制

#### 3. 支付错误
- **支付失败**：提供重试选项和替代支付方式
- **网络超时**：实现支付状态查询和补偿机制
- **订阅过期**：优雅降级到免费功能，发送续费提醒

#### 4. 系统错误处理
```typescript
class ErrorHandler {
  static handleAPIError(error: APIError): ErrorResponse {
    switch (error.type) {
      case 'AUTHENTICATION_ERROR':
        return { code: 401, message: '认证失败，请重新登录' }
      case 'PAYMENT_ERROR':
        return { code: 402, message: '支付处理失败，请重试' }
      case 'RATE_LIMIT_ERROR':
        return { code: 429, message: '请求过于频繁，请稍后重试' }
      default:
        return { code: 500, message: '系统错误，请联系技术支持' }
    }
  }
}
```

## 测试策略

### 测试层级

#### 1. 单元测试
- **覆盖率目标**：80%以上
- **测试框架**：Jest + React Testing Library
- **重点测试**：业务逻辑、数据处理、API接口

#### 2. 集成测试
- **API集成测试**：使用Supertest测试API端点
- **数据库集成测试**：使用测试数据库验证数据操作
- **第三方服务集成**：Mock外部服务进行测试

#### 3. 端到端测试
- **测试框架**：Playwright
- **关键用户流程**：注册登录、内容订阅、支付流程
- **跨浏览器测试**：Chrome、Firefox、Safari

#### 4. 性能测试
- **负载测试**：使用Artillery测试API性能
- **前端性能**：Lighthouse CI集成
- **数据库性能**：查询优化和索引测试

### 测试环境配置
```typescript
// 测试配置示例
const testConfig = {
  database: {
    host: 'localhost',
    port: 5432,
    database: 'ivd_news_test',
    username: 'test_user',
    password: 'test_password'
  },
  redis: {
    host: 'localhost',
    port: 6379,
    db: 1 // 使用不同的数据库
  },
  mockServices: {
    ciam: true,
    wechatPay: true,
    alipay: true,
    newsSources: true
  }
}
```

### 持续集成测试流程
1. **代码提交触发**：自动运行单元测试和代码质量检查
2. **PR创建**：运行完整测试套件包括集成测试
3. **部署前验证**：执行端到端测试和性能测试
4. **生产监控**：实时错误监控和性能指标收集

这个设计文档涵盖了需求文档中的所有功能要求，提供了清晰的技术架构和实现方案。设计重点考虑了系统的可扩展性、性能和用户体验，同时确保了与腾讯云生态系统的良好集成。