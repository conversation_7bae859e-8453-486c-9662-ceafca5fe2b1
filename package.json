{"name": "ivd-news-aggregation", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "tsx src/db/seed.ts"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-label": "^2.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "15.4.4", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "pg": "^8.13.1", "drizzle-orm": "^0.36.4", "drizzle-kit": "^0.30.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^11.0.3", "redis": "^4.7.0", "bull": "^4.16.3", "ws": "^8.18.0", "next-auth": "^4.24.10", "zod": "^3.24.1", "react-hook-form": "^7.54.2", "@hookform/resolvers": "^3.10.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/pg": "^8.11.10", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.13", "eslint": "^9", "eslint-config-next": "15.4.4", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "tailwindcss": "^4", "tsx": "^4.19.2", "tw-animate-css": "^1.3.6", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}