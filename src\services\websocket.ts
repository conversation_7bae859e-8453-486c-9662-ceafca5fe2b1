import { WebSocketServer, WebSocket } from 'ws';
import { IncomingMessage } from 'http';
import { parse } from 'url';
import jwt from 'jsonwebtoken';

interface AuthenticatedWebSocket extends WebSocket {
  userId?: string;
  subscriptions?: string[];
}

interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'ping' | 'pong';
  data?: any;
}

interface NotificationMessage {
  type: 'new_article' | 'source_update' | 'system_notification';
  data: any;
  timestamp: Date;
}

export class WebSocketManager {
  private wss: WebSocketServer;
  private clients: Map<string, AuthenticatedWebSocket[]> = new Map();

  constructor(port: number = 8080) {
    this.wss = new WebSocketServer({ 
      port,
      verifyClient: this.verifyClient.bind(this),
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    console.log(`WebSocket服务器启动在端口 ${port}`);
  }

  /**
   * 验证客户端连接
   */
  private verifyClient(info: { origin: string; secure: boolean; req: IncomingMessage }): boolean {
    try {
      const url = parse(info.req.url || '', true);
      const token = url.query.token as string;

      if (!token) {
        return false;
      }

      // 验证JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'secret') as any;
      
      // 将用户信息附加到请求对象
      (info.req as any).userId = decoded.id;
      
      return true;
    } catch (error) {
      console.error('WebSocket认证失败:', error);
      return false;
    }
  }

  /**
   * 处理新的WebSocket连接
   */
  private handleConnection(ws: AuthenticatedWebSocket, req: IncomingMessage) {
    const userId = (req as any).userId;
    ws.userId = userId;
    ws.subscriptions = [];

    console.log(`用户 ${userId} 建立WebSocket连接`);

    // 添加到客户端列表
    if (!this.clients.has(userId)) {
      this.clients.set(userId, []);
    }
    this.clients.get(userId)!.push(ws);

    // 发送欢迎消息
    this.sendToClient(ws, {
      type: 'system_notification',
      data: { message: '连接成功' },
      timestamp: new Date(),
    });

    // 处理消息
    ws.on('message', (data) => {
      try {
        const message: WebSocketMessage = JSON.parse(data.toString());
        this.handleMessage(ws, message);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    });

    // 处理连接关闭
    ws.on('close', () => {
      this.handleDisconnection(ws);
    });

    // 处理错误
    ws.on('error', (error) => {
      console.error(`WebSocket错误 (用户 ${userId}):`, error);
    });

    // 设置心跳检测
    this.setupHeartbeat(ws);
  }

  /**
   * 处理客户端消息
   */
  private handleMessage(ws: AuthenticatedWebSocket, message: WebSocketMessage) {
    switch (message.type) {
      case 'subscribe':
        this.handleSubscribe(ws, message.data);
        break;
      case 'unsubscribe':
        this.handleUnsubscribe(ws, message.data);
        break;
      case 'ping':
        this.sendToClient(ws, {
          type: 'system_notification',
          data: { type: 'pong' },
          timestamp: new Date(),
        });
        break;
      default:
        console.warn('未知的WebSocket消息类型:', message.type);
    }
  }

  /**
   * 处理订阅
   */
  private handleSubscribe(ws: AuthenticatedWebSocket, data: { categories?: string[], sources?: string[] }) {
    if (!ws.subscriptions) {
      ws.subscriptions = [];
    }

    if (data.categories) {
      ws.subscriptions.push(...data.categories.map(cat => `category:${cat}`));
    }

    if (data.sources) {
      ws.subscriptions.push(...data.sources.map(source => `source:${source}`));
    }

    // 去重
    ws.subscriptions = [...new Set(ws.subscriptions)];

    this.sendToClient(ws, {
      type: 'system_notification',
      data: { 
        message: '订阅成功',
        subscriptions: ws.subscriptions 
      },
      timestamp: new Date(),
    });

    console.log(`用户 ${ws.userId} 订阅了:`, ws.subscriptions);
  }

  /**
   * 处理取消订阅
   */
  private handleUnsubscribe(ws: AuthenticatedWebSocket, data: { categories?: string[], sources?: string[] }) {
    if (!ws.subscriptions) return;

    const toRemove: string[] = [];

    if (data.categories) {
      toRemove.push(...data.categories.map(cat => `category:${cat}`));
    }

    if (data.sources) {
      toRemove.push(...data.sources.map(source => `source:${source}`));
    }

    ws.subscriptions = ws.subscriptions.filter(sub => !toRemove.includes(sub));

    this.sendToClient(ws, {
      type: 'system_notification',
      data: { 
        message: '取消订阅成功',
        subscriptions: ws.subscriptions 
      },
      timestamp: new Date(),
    });

    console.log(`用户 ${ws.userId} 取消订阅:`, toRemove);
  }

  /**
   * 处理连接断开
   */
  private handleDisconnection(ws: AuthenticatedWebSocket) {
    const userId = ws.userId;
    if (!userId) return;

    const userClients = this.clients.get(userId);
    if (userClients) {
      const index = userClients.indexOf(ws);
      if (index > -1) {
        userClients.splice(index, 1);
      }

      if (userClients.length === 0) {
        this.clients.delete(userId);
      }
    }

    console.log(`用户 ${userId} 断开WebSocket连接`);
  }

  /**
   * 设置心跳检测
   */
  private setupHeartbeat(ws: AuthenticatedWebSocket) {
    const interval = setInterval(() => {
      if (ws.readyState === WebSocket.OPEN) {
        this.sendToClient(ws, {
          type: 'system_notification',
          data: { type: 'ping' },
          timestamp: new Date(),
        });
      } else {
        clearInterval(interval);
      }
    }, 30000); // 30秒心跳
  }

  /**
   * 发送消息给特定客户端
   */
  private sendToClient(ws: AuthenticatedWebSocket, message: NotificationMessage) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  /**
   * 广播新文章通知
   */
  public broadcastNewArticle(article: any) {
    const message: NotificationMessage = {
      type: 'new_article',
      data: article,
      timestamp: new Date(),
    };

    this.clients.forEach((userClients, userId) => {
      userClients.forEach(ws => {
        if (this.shouldReceiveArticle(ws, article)) {
          this.sendToClient(ws, message);
        }
      });
    });
  }

  /**
   * 判断用户是否应该接收文章通知
   */
  private shouldReceiveArticle(ws: AuthenticatedWebSocket, article: any): boolean {
    if (!ws.subscriptions || ws.subscriptions.length === 0) {
      return true; // 默认接收所有通知
    }

    // 检查分类订阅
    if (article.category && ws.subscriptions.includes(`category:${article.category}`)) {
      return true;
    }

    // 检查新闻源订阅
    if (article.sourceId && ws.subscriptions.includes(`source:${article.sourceId}`)) {
      return true;
    }

    return false;
  }

  /**
   * 广播系统通知
   */
  public broadcastSystemNotification(notification: { message: string; type?: string; data?: any }) {
    const message: NotificationMessage = {
      type: 'system_notification',
      data: notification,
      timestamp: new Date(),
    };

    this.clients.forEach((userClients) => {
      userClients.forEach(ws => {
        this.sendToClient(ws, message);
      });
    });
  }

  /**
   * 发送通知给特定用户
   */
  public sendToUser(userId: string, message: NotificationMessage) {
    const userClients = this.clients.get(userId);
    if (userClients) {
      userClients.forEach(ws => {
        this.sendToClient(ws, message);
      });
    }
  }

  /**
   * 获取在线用户数量
   */
  public getOnlineUsersCount(): number {
    return this.clients.size;
  }

  /**
   * 获取总连接数
   */
  public getTotalConnections(): number {
    let total = 0;
    this.clients.forEach(userClients => {
      total += userClients.length;
    });
    return total;
  }

  /**
   * 关闭WebSocket服务器
   */
  public close() {
    this.wss.close();
    console.log('WebSocket服务器已关闭');
  }
}

// 创建全局WebSocket管理器实例
export const wsManager = new WebSocketManager(
  parseInt(process.env.WEBSOCKET_PORT || '8080')
);
