'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { signIn } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { phoneRegisterSchema } from '@/lib/auth';

type PhoneAuthFormData = z.infer<typeof phoneRegisterSchema>;

interface PhoneAuthFormProps {
  mode: 'login' | 'register';
  onSwitchToEmail?: () => void;
}

export function PhoneAuthForm({ mode, onSwitchToEmail }: PhoneAuthFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingCode, setIsSendingCode] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [countdown, setCountdown] = useState(0);
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const form = useForm<PhoneAuthFormData>({
    resolver: zodResolver(phoneRegisterSchema),
    defaultValues: {
      phone: '',
      code: '',
      password: '',
    },
  });

  const phone = form.watch('phone');

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const sendVerificationCode = async () => {
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      setError('请输入有效的手机号');
      return;
    }

    setIsSendingCode(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/sms/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ phone }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || '发送验证码失败');
        return;
      }

      setCountdown(60);
    } catch (error) {
      setError('发送验证码失败，请稍后重试');
    } finally {
      setIsSendingCode(false);
    }
  };

  const onSubmit = async (data: PhoneAuthFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      if (mode === 'register') {
        // Register with phone
        const response = await fetch('/api/auth/register/phone', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        const result = await response.json();

        if (!response.ok) {
          setError(result.error || '注册失败');
          return;
        }

        setSuccess(true);
        setTimeout(() => {
          router.push('/auth/signin');
        }, 2000);
      } else {
        // Login with phone
        const result = await signIn('phone', {
          phone: data.phone,
          code: data.code,
          redirect: false,
        });

        if (result?.error) {
          setError('手机号或验证码错误');
        } else {
          router.push('/dashboard');
        }
      }
    } catch (error) {
      setError(mode === 'register' ? '注册失败，请稍后重试' : '登录失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (success && mode === 'register') {
    return (
      <div className="w-full max-w-md mx-auto text-center space-y-4">
        <div className="text-green-600">
          <h2 className="text-xl font-semibold">注册成功！</h2>
          <p className="text-sm text-muted-foreground">
            正在跳转到登录页面...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">
          {mode === 'register' ? '手机注册' : '手机登录'}
        </h1>
        <p className="text-muted-foreground">
          {mode === 'register' ? '使用手机号创建账户' : '使用手机号登录'}
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>手机号</FormLabel>
                <FormControl>
                  <Input
                    type="tel"
                    placeholder="请输入手机号"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormLabel>验证码</FormLabel>
                <div className="flex gap-2">
                  <FormControl>
                    <Input
                      placeholder="请输入验证码"
                      {...field}
                    />
                  </FormControl>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={sendVerificationCode}
                    disabled={isSendingCode || countdown > 0}
                    className="whitespace-nowrap"
                  >
                    {isSendingCode
                      ? '发送中...'
                      : countdown > 0
                      ? `${countdown}s`
                      : '发送验证码'}
                  </Button>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          {mode === 'register' && (
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>密码</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="请输入密码（至少6位）"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {error && (
            <div className="text-sm text-destructive text-center">
              {error}
            </div>
          )}

          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading
              ? mode === 'register'
                ? '注册中...'
                : '登录中...'
              : mode === 'register'
              ? '注册'
              : '登录'}
          </Button>
        </form>
      </Form>

      <div className="text-center">
        <Button
          variant="link"
          onClick={onSwitchToEmail}
          disabled={isLoading}
        >
          使用邮箱{mode === 'register' ? '注册' : '登录'}
        </Button>
      </div>
    </div>
  );
}
