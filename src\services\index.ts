// Service interfaces and base implementations for the IVD News Aggregation system

import type {
  User,
  NewsSource,
  Article,
  SubscriptionPlan,
  UserSubscription,
  Payment,
  SearchFilters,
  APIResponse,
} from '@/types'

/**
 * Authentication Service Interface
 */
export interface AuthService {
  // Registration methods
  registerWithEmail(email: string, password: string): Promise<User>
  registerWithPhone(phone: string, code: string): Promise<User>
  socialLogin(provider: 'wechat' | 'qq', token: string): Promise<User>
  passwordlessLogin(email: string): Promise<void>

  // Authentication methods
  login(credentials: { email?: string; phone?: string; password?: string }): Promise<{
    user: User
    token: string
  }>
  logout(): Promise<void>
  refreshToken(token: string): Promise<string>

  // CIAM integration
  ciamAuthenticate(token: string): Promise<User>
  resetPassword(email: string): Promise<void>

  // User management
  getCurrentUser(): Promise<User | null>
  updateUserProfile(userId: string, profile: Partial<User>): Promise<User>
}

/**
 * Content Aggregation Service Interface
 */
export interface ContentService {
  // Source management
  getAvailableSources(): Promise<NewsSource[]>
  subscribeToSource(
    userId: string,
    sourceId: string,
    categories: string[]
  ): Promise<void>
  unsubscribeFromSource(userId: string, sourceId: string): Promise<void>

  // Content aggregation
  aggregateContent(userId: string): Promise<Article[]>
  searchContent(query: string, filters: SearchFilters): Promise<Article[]>
  getArticleById(id: string): Promise<Article | null>

  // Content interaction
  saveArticle(userId: string, articleId: string): Promise<void>
  markAsRead(userId: string, articleId: string): Promise<void>
  shareArticle(articleId: string, method: 'email' | 'wechat' | 'link'): Promise<string>

  // Real-time updates
  setupRealTimeUpdates(userId: string): WebSocket
}

/**
 * Subscription Management Service Interface
 */
export interface SubscriptionService {
  // Plan management
  getSubscriptionPlans(): Promise<SubscriptionPlan[]>
  createSubscription(userId: string, planId: string): Promise<UserSubscription>
  cancelSubscription(subscriptionId: string): Promise<void>
  upgradeSubscription(subscriptionId: string, newPlanId: string): Promise<UserSubscription>

  // Payment processing
  processWeChatPayment(amount: number, userId: string): Promise<Payment>
  processAlipayPayment(amount: number, userId: string): Promise<Payment>

  // Recurring payments
  setupRecurringPayment(subscriptionId: string): Promise<void>
  handlePaymentFailure(subscriptionId: string): Promise<void>

  // Subscription status
  getSubscriptionStatus(userId: string): Promise<UserSubscription | null>
  isFeatureAvailable(userId: string, feature: string): Promise<boolean>
}

/**
 * Notification Service Interface
 */
export interface NotificationService {
  // Preference management
  updateNotificationPreferences(
    userId: string,
    preferences: any
  ): Promise<void>

  // Notification sending
  sendRegulatoryAlert(content: Article, targetUsers: string[]): Promise<void>
  sendMarketUpdate(content: Article, targetUsers: string[]): Promise<void>
  sendPaymentReminder(userId: string, subscription: UserSubscription): Promise<void>

  // Notification history
  getNotificationHistory(userId: string): Promise<any[]>
  markNotificationAsRead(userId: string, notificationId: string): Promise<void>
}

/**
 * Admin Service Interface
 */
export interface AdminService {
  // Source management
  addNewsSource(source: Omit<NewsSource, 'id' | 'createdAt'>): Promise<NewsSource>
  updateNewsSource(sourceId: string, updates: Partial<NewsSource>): Promise<NewsSource>
  deleteNewsSource(sourceId: string): Promise<void>

  // System monitoring
  getSystemHealth(): Promise<{
    sources: { id: string; status: 'healthy' | 'error'; lastUpdate: Date }[]
    userEngagement: { activeUsers: number; totalUsers: number }
    contentFreshness: { averageAge: number; staleContent: number }
  }>

  // User management
  getUsers(page: number, limit: number): Promise<{ users: User[]; total: number }>
  suspendUser(userId: string): Promise<void>
  reactivateUser(userId: string): Promise<void>
}

/**
 * Base API client for making HTTP requests
 */
export class APIClient {
  private baseURL: string
  private token: string | null = null

  constructor(baseURL: string) {
    this.baseURL = baseURL
  }

  setToken(token: string) {
    this.token = token
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      const data = await response.json()

      if (!response.ok) {
        return {
          success: false,
          error: data.message || 'Request failed',
        }
      }

      return {
        success: true,
        data,
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
      }
    }
  }

  async get<T>(endpoint: string): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' })
  }

  async post<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<APIResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' })
  }
}